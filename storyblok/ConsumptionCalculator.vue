<template>
  <section
    v-editable="blok"
    class="container mx-auto grid md:grid-cols-3 gap-12 my-12 place-items-center"
  >
    <h1>{{ blok.title }}</h1>
    <div class="relative mb-6">
      <label for="yearly-distance" class="">{{ blok.distance_label }} {{ yearlyDistanceValue }}km</label>
      <input
          id="yearly-distance"
          v-model="yearlyDistanceValue"
          type="range"
          min="5000"
          max="100000"
          step="1000"
          class="w-full h-4 range-lg bg-gray-200 rounded-lg appearance-none cursor-pointer"
      >
      <span class="text-sm text-gray-500 absolute start-0 -bottom-6">5 000</span>
      <span class="text-sm text-gray-500 absolute start-1/3 -translate-x-1/2 rtl:translate-x-1/2 -bottom-6">35 000</span>
      <span class="text-sm text-gray-500 absolute start-2/3 -translate-x-1/2 rtl:translate-x-1/2 -bottom-6">70 000</span>
      <span class="text-sm text-gray-500 absolute end-0 -bottom-6">100 000</span>
    </div>
    <div class="relative mb-6">
      <label for="consumption" class="">{{ blok.consumption_label }} {{ consumptionValue }}l/100km</label>
      <input
          id="consumption"
          v-model="consumptionValue"
          type="range"
          min="3"
          max="20"
          step="1"
          class="w-full h-4 range-lg bg-gray-200 rounded-lg appearance-none cursor-pointer"
      >
      <span class="text-sm text-gray-500 absolute start-0 -bottom-6">3</span>
      <span class="text-sm text-gray-500 absolute start-1/3 -translate-x-1/2 rtl:translate-x-1/2 -bottom-6">10</span>
      <span class="text-sm text-gray-500 absolute start-2/3 -translate-x-1/2 rtl:translate-x-1/2 -bottom-6">15</span>
      <span class="text-sm text-gray-500 absolute end-0 -bottom-6">20</span>
    </div>
    <div class="text-xl font-semibold">
      {{ blok.result_label }} ~{{ calculatedSavings }}€
    </div>
  </section>
</template>

<script setup>
const props = defineProps({ blok: Object })

import { ref, computed } from 'vue'

const yearlyDistanceValue = ref(50000)
const consumptionValue = ref(7)

const GAS_PRICE = 0.895
const GAS_CONSUMPTION_INCREASE = 1.2

const calculatedSavings = computed(() => {
  const yearlyConsumption = (yearlyDistanceValue.value / 100) * consumptionValue.value

  const petrolCost = yearlyConsumption * props.blok.petrol_price
  const gasCost = (yearlyConsumption * GAS_CONSUMPTION_INCREASE) * props.blok.lpg_price

  return Math.round(petrolCost - gasCost)
})
</script>
